cfg_boss = {

--BLIPS FOR JOB CENTERS
BlipCenterSprite = 498,
BlipCenterColor = 3,
BlipCenterText = 'Job Center',

MarkerSprite = 27,
MarkerColor = {66, 135, 245},
MarkerSize = 1.1,

LocationsJobCenters = { -- If you want you can setup locations to change jobs (Leave without entiries if you dont want locations) (ADDS 0.02 MS)
	--{coords = vector3(-139.32, -632.2, 168.82), blip = false}
},

--Boss menu locations
BossMenuLocations = {
	{coords = vector3(2513.5669, -446.6433, 106.9129), job = "police", label = "POLISI"},
	{coords = vector3(-2819.6743, -64.4474, 18.6113), job = "ambulance", label = "EMS"},
	{coords = vector3(-2819.6743, -64.4474, 18.6113), job = "emt", label = "EMT"},
	{coords = vector3(706.48, -304.37, 59.9), job = "pedagang", label = "Lounge Dgc"},
	{coords = vector3(-1849.96, -1192.89, 13.03), job = "pedagang2", label = "Lounge DHM"},
	{coords = vector3(86.4208, -1739.7317, 29.9187), job = "pedagang3", label = "Pam Resto"},
	{coords = vector3(1194.09, 2648.56, 38.37), job = "mechanic", label = "MEKANIK"},
	{coords = vector3(-3030.86, 85.99, 12.82), job = "taxi", label = "PRACU"},
	{coords = vector3(100.2181, 6620.4512, 32.4353), job = "biker", label = "Vitlauss MC"},
	{coords = vector3(-2175.6758, 4294.7852, 49.0620), job = "biker2", label = "MAYANS MC"},
	{coords = vector3(732.4676, 2523.4675, 73.3713), job = "biker3", label = "Beatless MC"},
	{coords = vector3(1798.4420, 3853.7927, 34.4075), job = "biker4", label = "Brotherhood MC"},
	{coords = vector3(-1516.9236, 851.5366, 181.5947), job = "biker5", label = "Legion One MC"},
	-- {coords = vector3(732.4675, 2523.3145, 73.3709), job = "biker6", label = "Blacklist MC"},
	{coords = vector3(-43.9165, 1959.8414, 190.3534), job = "mafia2", label = "Bels"},
	{coords = vector3(-322.2078, 6171.1475, 32.3128), job = "mafia", label = "Claude"},
	{coords = vector3(-9.3327, -1441.3933, 31.1015), job = "gang", label = "Forum Drive Families"},
	{coords = vector3(-617.4086, -1622.6823, 33.0106), job = "gang2", label = "SL"},
	{coords = vector3(-1558.5184, -399.1680, 41.9877), job = "gang3", label = "SYNDICATE"},
	{coords = vector3(-239.2257, -1515.6517, 33.3773), job = "gang4", label = "DOBRAK"},
	{coords = vector3(-830.3978, -420.3956, 36.7644), job = "gang5", label = "SOTG"},
	{coords = vector3(987.1927, -92.8668, 74.8457), job = "biker7", label = "GRAVEREAPERS"},
	{coords = vector3(-560.6941, 281.6083, 85.6764), job = "gang7", label = "SATANIC"},
	{coords = vector3(736.6816, -1063.8163, 22.1684), job = "gang8", label = "BROKER"},
	-- {coords = vector3(-1192.6577, -215.3584, 37.9448), job = "gang9", label = "CSR"},
	{coords = vector3(113.4738, -1973.9799, 21.3202), job = "gang10", label = "TROUMA"},
	{coords = vector3(561.6821, -1747.3370, 33.4426), job = "gang11", label = "780 PARK BOYZ AVENUE"},
	{coords = vector3(-1109.8153, -1640.7432, 4.6405), job = "gang12", label = "Revenant"},
	{coords = vector3(485.8266, 201.5772, 108.3095), job = "gang13", label = "SATYR"},
	{coords = vector3(313.3111, -2040.4530, 20.9364), job = "gang14", label = "Yellow Crown"},
	{coords = vector3(83.8176, 190.0924, 105.2634), job = "gang15", label = "EXCELSOIR"},
	{coords = vector3(-1422.2704, -640.6149, 28.6734), job = "gang16", label = "SFxRYU"},
	{coords = vector3(-749.2158, -228.2340, 48.5164), job = "gang20", label = "WDS"},
	{coords = vector3(1119.7686, 2631.0547, 37.9964), job = "biker8", label = "SOA"},
	{coords = vector3(-457.8975, 1104.5643, 331.6300), job = "state", label = "Presiden"},
	{coords = vector3(564.25, -179.7, 59.0), job = "mechanic2", label = "Kerta Garage"},
	{coords = vector3(86.4208, -1739.7317, 29.9187), job = "mechanic3", label = "Pam Garage"},
	{coords = vector3(21.8109, 6529.2358, 43.9295), job = "mechanic4", label = "Mechanic"},
	{coords = vector3(-1598.0355, -34.1776, 58.1927), job = "mafia3", label = "ALTAR"},
	{coords = vector3(-1339.1908, -941.4564, 12.3534), job = "gang17", label = "BAHUI"},
	{coords = vector3(-41.2717, 327.3509, 112.7925), job = "mafia5", label = "TBO"},
	{coords = vector3(1994.8711, 3046.3125, 47.2152), job = "biker9", label = "WHITE TIGER"},
	{coords = vector3(-1148.1550, -1999.6285, 13.180), job = "biker10", label = "BLACKDOG"},
	{coords = vector3(-806.5389, 167.4858, 76.7408), job = "mafia4", label = "Laswell Family"},
	{coords = vector3(407.6003, 242.5499, 92.0523), job = "pedagang4", label = "NIX GALAXY CLUB"},
	{coords = vector3(301.4915, 202.9483, 104.3871), job = "mafia6", label = "BLADE RED DRAGON"},
	{coords = vector3(725.4503, -1066.8026, 28.3110), job = "biker11", label = "RJMC"},
	{coords = vector3(-282.4540, -2031.5961, 30.1456), job = "gang21", label = "ATLET"},
	{coords = vector3(100.3573, 53.8455, 73.5209), job = "gang22", label = "PEMABUK 666"},
	{coords = vector3(225.9957, -9.6420, 73.8028), job = "gang23", label = "NORT FELLAS"},
	{coords = vector3(1394.5042, 3601.7283, 38.9419), job = "biker12", label = "CBMC"},
	{coords = vector3(-1564.7965, 776.1448, 189.1944), job = "mafia7", label = "SILENZIA"},
	{coords = vector3(-1063.4465, -246.5569, 44.9178), job = "gadai", label = "Pegadaian"},
	{coords = vector3(-8.8163, -97.0655, 57.0853), job = "digital", label = "Digital Den"},
	{coords = vector3(-1192.8431, -215.3138, 37.9448), job = "gang24", label = "All Stars"},
	{coords = vector3(752.0406, -1202.0208, 24.3005), job = "gang25", label = "Silent Phantom"},
	{coords = vector3(-1590.7539, -843.3001, 10.1649), job = "dhmapart", label = "DHM APART"},
	{coords = vector3(-514.8991, 2740.9834, 40.9906), job = "klinik", label = "KLINIK MANCHINEL"},
	{coords = vector3(-1134.7240, 4948.9199, 222.2685), job = "gang26", label = "Satim"},
	{coords = vector3(-1889.2487, 2051.2793, 140.9855), job = "mafia8", label = "DOF"},
	{coords = vector3(-541.2327, -180.2730, 38.2291), job = "doj", label = "DOJ"},
	{coords = vector3(-1079.7660, -1679.5702, 4.5752), job = "gang27", label = "PAM DESTROYER"},
	{coords = vector3(1967.0864, 4634.1113, 41.1017), job = "biker13", label = "HRB MC"},
	{coords = vector3(472.1282, -1310.7853, 29.2183), job = "gang28", label = "STREET OF WOLVES"},
	{coords = vector3(203.25, 1167.9, 230.54), job = "pdm", label = "Showroom Kamek"},
},

--Boss menu users by grade name and their permissions
BossMenuUsers = {
	['dirr'] = {canWithdraw = true, canDeposit = true, canHire = true, canRank = true, canFire = true, canBonus = false},
	['boss'] = {canWithdraw = true, canDeposit = true, canHire = true, canRank = true, canFire = true, canBonus = false},
	['viceboss'] = {canWithdraw = true, canDeposit = true, canHire = true, canRank = true, canFire = true, canBonus = false},
	['chef'] = {canWithdraw = false, canDeposit = true, canHire = true, canRank = true, canFire = true, canBonus = false},
	['consigliere'] = {canWithdraw = false, canDeposit = true, canHire = false, canRank = false, canFire = false, canBonus = false},
	['pemerintah'] = {canWithdraw = true, canDeposit = true, canHire = true, canRank = true, canFire = true, canBonus = false}
	--['recruit'] = {canWithdraw = false, canDeposit = true, canHire = false, canRank = false, canFire = false, canBonus = false}
},

DefaultJobsInJobCenter = { -- Jobs that can be added by going to the job center. For icons use https://fontawesome.com/
	{job = 'miner', label = "Penambang", icon = "fas fa-gem", description = "You mine stuff and get materials you can sell"},
	{job = 'fisherman', label = "Nelayan", icon = "fas fa-fish", description = "Get fish and sell for profit."},
	{job = 'slaughter', label = "Tukang Ayam", icon = "fa-thin fa-duck", description = "Get fish and sell for profit."},
	{job = 'tailor', label = "Tukang Jahit", icon = "fas fa-user-nurse", description = "Get fish and sell for profit."},
	{job = 'lumberjack', label = "Tukang Kayu", icon = "fas fa-user-helmet-safety", description = "Get fish and sell for profit."},
	{job = 'busdriver', label = "Supir Bus", icon = "fas fa-van-shuttle", description = "Get fish and sell for profit."},
	{job = 'unemployed', label = "Nganggur", icon = "fas fa-shield", description = "Get fish and sell for profit."},
	{job = 'petani', label = "Petani", icon = "fas fa-shield", description = "Get fish and sell for profit."}
},


Text = {

	['open_jobcenter_ui_hologram'] = '[~b~E~w~] Open Job Center',
	['promoted'] = 'You have been promoted',
	['promoted_other'] = 'You have been promoted in another job!',
	['fired'] = 'You have been fired',
	['fired_other'] = 'You have been fired',
	['hired'] = 'You have been hired',
	['bossmenu_hologram'] = '[~b~E~w~] Open Boss Menu',
	['action_success'] = 'Action successful',
	['action_unsuccessful'] = 'Action unsuccessful',
	['cant_access_bossmenu'] = 'You cant access boss menu',
	['insufficent_balance'] = 'Insufficent balance',
	['bonus_given'] = 'Bonus has been given!',
	['bonus_recieved'] = 'You recieved a bonus! +$'

}


}
-- Only change if you know what are you doing!
function SendTextMessage(msg)

		exports['ox_lib']:SendAlert('inform', msg)

end
