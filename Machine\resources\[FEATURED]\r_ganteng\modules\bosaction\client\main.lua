-- CORE MULTIJOB
local ESX = exports['r_core']:getSharedObject()

local job = "unemployed"
local grade = 0

Citizen.CreateThread(
    function()

        while ESX.GetPlayerData().job == nil do
            Citizen.Wait(10)
        end

        job = ESX.GetPlayerData().job.name
        grade = ESX.GetPlayerData().job.grade
    end
)

AddEventHandler('esx:nui_ready', function()
    BuatFrame('bosaction', 'nui://' .. GetCurrentResourceName() .. '/modules/bosaction/data/html/form.html')
end)

RegisterNetEvent("esx:setJob")
AddEventHandler("esx:setJob", function(j)
    job = j.name
    grade = j.grade
end)

function openCenter()
    TriggerScreenblurFadeIn(1000)
    PokusFrame('bosaction', true)
    KirimFrameMessage('bosaction',
        {
            type = "openCenter",
            job = {job = job, grade = grade},
            center = json.encode(cfg_boss.DefaultJobsInJobCenter)
        }
    )
end

-- Global variables to store current boss menu state
local currentBossJob = nil
local currentBossLabel = nil

function openBossMenu(j, l)
    currentBossJob = j
    currentBossLabel = l

    lib.progressBar({
        label = 'Membuka Boss Menu',
        duration = 1000,
      })
    ESX.TriggerServerCallback("core_jobutilities:getBossMenuData", function(grades, employees, societyMoney, gradename)
        if cfg_boss.BossMenuUsers[gradename] ~= nil then
            local playerData = ESX.GetPlayerData()
            local playerJob = playerData.job and playerData.job.name or "unknown"
            local playerGrade = playerData.job and playerData.job.grade or 0

            if playerJob == j then
                PokusFrame('bosaction', true)
                KirimFrameMessage('bosaction',
                    {
                        type = "openBoss",
                        job = {job = playerJob, grade = playerGrade},
                        employees = employees or {},
                        grades = grades or {},
                        fund = societyMoney or 0,
                        bossJob = j,
                        bossLabel = l,
                        perms = cfg_boss.BossMenuUsers[gradename]
                    }
                )
            else
                SendTextMessage(cfg_boss.Text["cant_access_bossmenu"])
            end
        else
            SendTextMessage(cfg_boss.Text["cant_access_bossmenu"])
        end
    end, j)
end

-- Function to refresh boss menu data
function refreshBossMenu()
    if currentBossJob and currentBossLabel then
        print("^3[BOSSMENU] Refreshing boss menu for job: " .. currentBossJob .. "^0")
        ESX.TriggerServerCallback("core_jobutilities:getBossMenuData", function(grades, employees, societyMoney, gradename)
            print("^3[BOSSMENU] Received data - Employees: " .. #employees .. ", Grades: " .. #grades .. ", Money: " .. societyMoney .. "^0")
            if cfg_boss.BossMenuUsers[gradename] ~= nil then
                local playerData = ESX.GetPlayerData()
                local playerJob = playerData.job and playerData.job.name or "unknown"
                local playerGrade = playerData.job and playerData.job.grade or 0

                if playerJob == currentBossJob then
                    print("^3[BOSSMENU] Sending refresh message to UI^0")
                    KirimFrameMessage('bosaction',
                        {
                            type = "refreshBoss",
                            job = {job = playerJob, grade = playerGrade},
                            employees = employees or {},
                            grades = grades or {},
                            fund = societyMoney or 0,
                            bossJob = currentBossJob,
                            bossLabel = currentBossLabel,
                            perms = cfg_boss.BossMenuUsers[gradename]
                        }
                    )
                else
                    print("^1[BOSSMENU] Player job mismatch: " .. playerJob .. " vs " .. currentBossJob .. "^0")
                end
            else
                print("^1[BOSSMENU] No permissions found for grade: " .. (gradename or "nil") .. "^0")
            end
        end, currentBossJob)
    else
        print("^1[BOSSMENU] Cannot refresh - currentBossJob or currentBossLabel is nil^0")
    end
end



for _, v in ipairs(cfg_boss.BossMenuLocations) do
    exports.ox_target:addBoxZone({
        coords = vec3(v.coords[1], v.coords[2], v.coords[3]),
        size = vec3(1.5, 1.5, 1.5),
        rotation = 0.0,
        options = {
            {
                name = 'bossmenu',
                onSelect = function ()
                    openBossMenu(v.job, v.label)
                end,
                icon = 'fa-solid fa-user-tie',
                label = v.label or 'Open Boss Menu',
                distance = 2.0,
                job = v.job
            }
        }
    })
end

RegisterNUICallback("close", function(data)
    TriggerScreenblurFadeOut(1000)
    SetNuiFocus(false, false)

    -- Reset current boss menu state when closing
    currentBossJob = nil
    currentBossLabel = nil
end)
RegisterNUICallback("deposit", function(data)
    local job = data["job"]
    local amount = tonumber(data["amount"])
    print(amount)

    local success, message = lib.callback.await("core_jobutilities:deposit", false, job, amount)
    TriggerEvent('rise-notify:Client:SendAlert', { type = success and 'success' or 'error', text = message })

    -- Refresh boss menu if action was successful to update society money
    if success then
        Citizen.SetTimeout(500, function() -- Small delay to ensure database is updated
            refreshBossMenu()
        end)
    end
end)


RegisterNUICallback("withdraw", function(data)
    local job = data["job"]
    local amount = tonumber(data["amount"])
    print(amount)

    local success, message = lib.callback.await("core_jobutilities:withdraw", false, job, amount)
    TriggerEvent('rise-notify:Client:SendAlert', { type = success and 'success' or 'error', text = message })

    -- Refresh boss menu if action was successful to update society money
    if success then
        Citizen.SetTimeout(500, function() -- Small delay to ensure database is updated
            refreshBossMenu()
        end)
    end
end)


RegisterNUICallback("hire", function(data)
    local id = tonumber(data["id"])
    local job = tostring(data["job"])
    if not id or not job or job == "" then
        TriggerEvent('rise-notify:Client:SendAlert', { type = 'error', text = 'ID atau Job tidak valid!' })
        return
    end

    local success, message = lib.callback.await("core_jobutilities:hire", false, id, job)
    TriggerEvent('rise-notify:Client:SendAlert', { type = success and 'success' or 'error', text = message })

    -- Refresh boss menu if action was successful
    if success then
        print("^2[BOSSMENU] Hire successful, refreshing menu in 1 second...^0")
        Citizen.SetTimeout(1000, function() -- Increased delay for hire/fire operations
            print("^2[BOSSMENU] Refreshing boss menu after hire^0")
            refreshBossMenu()
        end)
    end
end)


RegisterNUICallback("fire", function(data)
    local identifier = data["identifier"]
    local job = tostring(data["job"])
    if not identifier or identifier == "" then
        TriggerEvent('rise-notify:Client:SendAlert', { type = 'error', text = 'Identifier tidak valid!' })
        return
    end
    if not job or job == "" then
        TriggerEvent('rise-notify:Client:SendAlert', { type = 'error', text = 'Job tidak valid!' })
        return
    end

    local success, message = lib.callback.await("core_jobutilities:fire", false, identifier, job)
    TriggerEvent('rise-notify:Client:SendAlert', { type = success and 'success' or 'error', text = message })

    -- Refresh boss menu if action was successful
    if success then
        print("^2[BOSSMENU] Fire successful, refreshing menu in 1 second...^0")
        Citizen.SetTimeout(1000, function() -- Increased delay for hire/fire operations
            print("^2[BOSSMENU] Refreshing boss menu after fire^0")
            refreshBossMenu()
        end)
    end
end)

RegisterNUICallback("givebonus", function(data)
    local identifier = data["identifier"]
    local amount = data["amount"]
    local job = data["job"]

    local success, message = lib.callback.await("core_jobutilities:givebonus", false, identifier, amount, job)
    TriggerEvent('rise-notify:Client:SendAlert', { type = success and 'success' or 'error', text = message })

    -- Refresh boss menu if action was successful (bonus doesn't change employee list but updates society money)
    if success then
        Citizen.SetTimeout(500, function() -- Small delay to ensure database is updated
            refreshBossMenu()
        end)
    end
end)


RegisterNUICallback("setrank", function(data)
    local identifier = data["identifier"]
    local job = data["job"]
    local rank = data["rank"]

    local success, message = lib.callback.await("core_jobutilities:setRank", false, identifier, job, rank)
    TriggerEvent('rise-notify:Client:SendAlert', { type = success and 'success' or 'error', text = message })

    -- Refresh boss menu if action was successful
    if success then
        Citizen.SetTimeout(500, function() -- Small delay to ensure database is updated
            refreshBossMenu()
        end)
    end
end)


RegisterNUICallback("removejob", function(data)
    local job = data["job"]
    local grade = data["grade"]
    local success, message = lib.callback.await("core_jobutilities:removeJob", false, job, grade)
    TriggerEvent('rise-notify:Client:SendAlert', { type = success and 'success' or 'error', text = message })
end)


RegisterNUICallback("addJob", function(data)
    local job = data["job"]

    local success, message = lib.callback.await("core_jobutilities:addJob", false, job)
    TriggerEvent('rise-notify:Client:SendAlert', { type = success and 'success' or 'error', text = message })
end)


RegisterNUICallback("changejob", function(data)
    local rnr = TriggerServerEvent
    rnr("core_jobutilities:changeJob", data["job"], data["grade"])
end)

RegisterNetEvent("core_jobutilities:sendMessage")
AddEventHandler("core_jobutilities:sendMessage", function(msg)
    SendTextMessage(msg)
end)

function DrawText3D(x, y, z, text)
    local onScreen, _x, _y = World3dToScreen2d(x, y, z)
    local px, py, pz = table.unpack(GetGameplayCamCoord())
    local dist = GetDistanceBetweenCoords(px, py, pz, x, y, z, 1)

    local scale = ((1 / dist) * 2) * (1 / GetGameplayCamFov()) * 100

    if onScreen then
        SetTextColour(255, 255, 255, 255)
        SetTextScale(0.0 * scale, 0.35 * scale)
        SetTextFont(4)
        SetTextProportional(1)
        SetTextCentre(true)

        SetTextDropshadow(1, 1, 1, 1, 255)

        BeginTextCommandWidth("STRING")
        AddTextComponentString(text)
        local height = GetTextScaleHeight(0.55 * scale, 4)
        local width = EndTextCommandGetWidth(4)

        SetTextEntry("STRING")
        AddTextComponentString(text)
        EndTextCommandDisplayText(_x, _y)
    end
end
