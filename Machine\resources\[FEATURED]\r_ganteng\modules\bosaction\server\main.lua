ESX = exports['r_core']:getSharedObject()

local webhook = {
	['police'] =
	'https://discord.com/api/webhooks/1369640884529790976/qEsbmqzjxEmPVfZe9zOCP_CclFHUWJAEAyEzT1fRi9IGoXH5PAD4opJ0z6n7RQIjFKvt',
	['ambulance'] =
	'https://discord.com/api/webhooks/1369640813650382928/LWs7w0lFvGtCu4UPYwCQolEVpASKAFvpw10ZWTFD7i_yLya3gZwtIexVIl1PC5pbP66H',
	['mechanic'] =
	'https://discord.com/api/webhooks/1369640965383524443/ouGRCTanVPF0e-mo1OqZ2rZNawj4Xn_5rWuonaLpFRY4A4fncTjgt2XKiI6zdIiV5oDz',
	['pedagang'] =
	'https://discord.com/api/webhooks/1369641163937677353/eJK5ueZQTSqakhTLr6eUJIYN5mJbJE_5F2YHbyZixVPZzjLoGv4Qqj3RbNCyLvDwAdRa',
	['pedagang2'] =
	'https://discord.com/api/webhooks/1369641462966386688/mVmqq-Je_YY6wwPc0MhMnEwoUHGGerpKVi623Accgahwt47Mhg22cOj58pBgA2YDNj1n',
    ['pedagang3'] =
    'https://discord.com/api/webhooks/1371985921565528104/h38jmLaT6RaIl8-Ds28Zi_PahtgY0bin-0-kl_EEZwOL5t09Qeo-DR7YDg_DHSIekaeL',
	['mechanic2'] =
	'https://discord.com/api/webhooks/1369641595011334185/XpSmxImYGybltLzEVVRMTEf2W9c85b6Nr7SDviEOV4DsK7b0UsUU2tBa4mOz1h2AS43v',
	['mechanic3'] =
	'https://discord.com/api/webhooks/1369641721608015985/trs42csLw-qLavg0IXez4laJ6BJoXAYkkpZ3vJb6pMgPUFmp-zjzuYH1PTzPswZa2M1K',
    ['mechanic4'] =
	'https://discord.com/api/webhooks/1371986205679554621/BBqyBcPRtpJuk2TnaU8jAtmmxvzO5RleWMAt7Hg2vxWdSHhpUOAzOp1u4Yi3KozrMNFE',
    ['gadai'] =
    'https://discord.com/api/webhooks/1378110698865037352/om8BMzUWoxpPRYCxgm1BEDkuz8g0W6CZyx0gD2UY7uuWQk0GWaP5jZKXLsmMcZTkafvs',
    ['digital'] =
    'https://discord.com/api/webhooks/1379854479381495889/h5hjPLC5BIZ9e5zcHzL73uGTYrvcCVGu90Km2ZSowGLGyYwU53v7AnyHoElps_3iwmaZ',
    ['klinik'] =
    'https://discord.com/api/webhooks/1379854625452069015/SYSEsuPx2oIJizumENj_o7dm2-gJswmKEw__MGEeVuWJkM5nqq9_x2wZzERXIVrdFoBP',
    ['dhmapart'] =
    'https://discord.com/api/webhooks/1380602966243016876/tx2kGpa5F--vFRngzzB7O9jeQ2c1MGjk0jC3E27cnIATFhUKWYJWn_rAcdh1xc7bg0M7',
}

local function kirimWebhook(hook, title, fields)
	if webhook[hook] and webhook[hook] == '' then return end
	local embedData = { {
		['title'] = title,
		['color'] = 255,
		['footer'] = {
			['text'] = "| Log Bossmenu | " .. os.date(),
			['icon_url'] =
			"https://cdn.discordapp.com/attachments/944789399852417096/1020099828266586193/blanc-800x800.png"
		},
		['fields'] = fields,
		['description'] = "",
		['author'] = {
			['name'] = "RNR`Society",
			['icon_url'] = "https://r1weh.github.io/6.png"
		}
	} }
	PerformHttpRequest(webhook[hook], nil, 'POST', json.encode({
		username = 'Log Boss Menu',
		embeds = embedData
	}), {
		['Content-Type'] = 'application/json'
	})
end

-- Remove the cached JobInfo system for real-time data
-- MySQL.ready is no longer needed for caching job data

ESX.RegisterServerCallback("core_jobutilities:getBossMenuData", function(source, cb, job)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return cb(nil) end

    print("^3[BOSSMENU SERVER] Fetching boss menu data for job: " .. job .. "^0")

    MySQL.Async.fetchAll("SELECT * FROM job_grades WHERE job_name = @job ORDER BY grade ASC", {
        ["@job"] = job
    }, function(grades)
        local gradeInfo = {}
        local jobGrades = {}

        print("^3[BOSSMENU SERVER] Found " .. #grades .. " grades for job: " .. job .. "^0")

        for _, grade in ipairs(grades) do
            jobGrades[grade.grade] = grade
            table.insert(gradeInfo, { grade = grade.grade, grade_label = grade.label })
        end

        MySQL.Async.fetchAll("SELECT * FROM users WHERE job = @job", {
            ["@job"] = job
        }, function(users)
            local employees = {}

            print("^3[BOSSMENU SERVER] Found " .. #users .. " employees for job: " .. job .. "^0")

            for _, v in ipairs(users) do
                local grade = tonumber(v.job_grade) or 0
                local gradeLabel = jobGrades[grade] and jobGrades[grade].label or "Unknown"

                table.insert(employees, {
                    identifier = v.identifier,
                    fullname = ((v.firstname or "") .. " " .. (v.lastname or "")):gsub("^%s*(.-)%s*$", "%1"),
                    grade = grade,
                    grade_label = gradeLabel
                })
                print("^3[BOSSMENU SERVER] Employee: " .. v.firstname .. " " .. v.lastname .. " (Grade: " .. grade .. ")^0")
            end

            TriggerEvent("esx_addonaccount:getSharedAccount", "society_" .. job, function(account)
                local societyMoney = account and account.money or 0
                print("^3[BOSSMENU SERVER] Sending data - Employees: " .. #employees .. ", Society Money: " .. societyMoney .. "^0")
                cb(gradeInfo, employees, societyMoney, xPlayer.getJob().grade_name)
            end)
        end)
    end)
end)

lib.callback.register("core_jobutilities:hire", function(source, id, job)
    local xTarget = ESX.GetPlayerFromId(id)
    local xPlayer = ESX.GetPlayerFromId(source)

    if not xTarget then
        return false, 'Gagal merekrut: pemain tidak ditemukan atau offline.'
    end

    if xPlayer.getJob().name ~= job then
        exports["r-amanaja"]:fg_BanPlayer(source, "Mencoba eksploitasi sistem hire bossmenu, target job: " .. job .. ", job dia: " .. xPlayer.getJob().name, true)
        return false, 'Anda tidak memiliki izin untuk merekrut ke pekerjaan ini.'
    end

    local lastJob = xTarget.getJob().name
    xTarget.setJob(job, 0)
    TriggerClientEvent('rise-notify:Client:SendAlert', xTarget.source, {
        type = 'success',
        text = 'Selamat! Anda sekarang bekerja sebagai ' .. job .. '.'
    })

    TriggerClientEvent('rise-notify:Client:SendAlert', source, {
        type = 'success',
        text = 'Pemain berhasil direkrut ke pekerjaan ' .. job .. '.'
    })

    kirimWebhook(job, 'Recruit Player', {
        { name = 'Yang Recruit',      value = xPlayer.getName() },
        { name = 'Identifier Recruit', value = xPlayer.identifier },
        { name = 'Yang Di Recruit',   value = xTarget.getName() },
        { name = 'Identifier Player', value = xTarget.identifier },
        { name = 'Job Sebelumnya',    value = lastJob },
        { name = 'Job Baru',          value = xTarget.getJob().name },
        { name = 'Grade Baru',        value = xTarget.getJob().grade_label },
    })

    return true, 'Pemain berhasil direkrut ke pekerjaan ' .. job .. '.'
end)

lib.callback.register("core_jobutilities:fire", function(source, identifier, job)
    local xPlayer = ESX.GetPlayerFromId(source)

    if xPlayer.getJob().name ~= job then
        exports["r-amanaja"]:fg_BanPlayer(source, "Mencoba eksploitasi sistem fire bossmenu, target job: " .. job .. ", job dia: " .. xPlayer.getJob().name, true)
        return false, 'Eksploitasi terdeteksi.'
    end

    local xTarget = ESX.GetPlayerFromIdentifier(identifier)
    local lastJob = job

    if xTarget then
        if xTarget.getJob().name == job then
            xTarget.setJob("unemployed", 0)

            TriggerClientEvent('rise-notify:Client:SendAlert', xTarget.source, {
                type = 'error',
                text = 'Maaf, anda telah diberhentikan dari pekerjaan ini.'
            })

            TriggerClientEvent('rise-notify:Client:SendAlert', source, {
                type = 'success',
                text = 'Karyawan berhasil dipecat.'
            })

            kirimWebhook(job, 'Pecat Player (Online)', {
                { name = 'Yang Pecat',         value = xPlayer.getName() },
                { name = 'Identifier Pecat',   value = xPlayer.identifier },
                { name = 'Yang Di Pecat',      value = xTarget.getName() },
                { name = 'Identifier Di Pecat',value = xTarget.identifier },
                { name = 'Job Sebelumnya',     value = lastJob },
                { name = 'Job Baru',           value = 'Unemployed' },
            })

            return true, 'Karyawan berhasil dipecat.'
        else
            return false, 'Target tidak memiliki pekerjaan tersebut.'
        end
    else
        local result = MySQL.query.await("SELECT job FROM users WHERE identifier = ?", { identifier })
        if result[1] and result[1].job == job then
            MySQL.query.await("UPDATE users SET job = ?, job_grade = ? WHERE identifier = ?", { 'unemployed', 0, identifier })

            kirimWebhook(job, 'Pecat Player (Offline)', {
                { name = 'Yang Pecat',         value = xPlayer.getName() },
                { name = 'Identifier Pecat',   value = xPlayer.identifier },
                { name = 'Yang Di Pecat',      value = identifier },
                { name = 'Job Sebelumnya',     value = lastJob },
                { name = 'Job Baru',           value = 'Unemployed' },
            })

            return true, 'Karyawan offline berhasil dipecat.'
        else
            return false, 'Target tidak memiliki pekerjaan tersebut atau tidak ditemukan.'
        end
    end
end)

lib.callback.register("core_jobutilities:deposit", function(source, job, amount)
    local xPlayer = ESX.GetPlayerFromId(source)
    local depositAmount = tonumber(amount)

    if not depositAmount or depositAmount <= 0 then
        return false, 'Jumlah deposit tidak valid!'
    end

    if xPlayer.getMoney() < depositAmount then
        return false, 'Uang kamu tidak cukup untuk deposit!'
    end

    if xPlayer.getJob().name ~= job then
        exports["r-amanaja"]:fg_BanPlayer(source, "Mencoba eksploitasi sistem deposit bossmenu. Target society: " .. job .. ", job dia: " .. xPlayer.getJob().name, true)
        return false, 'Eksploitasi terdeteksi.'
    end

    TriggerEvent('esx_addonaccount:getSharedAccount', 'society_' .. job, function(account)
        if account then
            xPlayer.removeMoney(depositAmount, "Society Deposit")
            account.addMoney(depositAmount)

            kirimWebhook(job, 'Deposit / Setor Uang Boss Menu', {
                { name = 'Nama Player',          value = xPlayer.getName() },
                { name = 'Identifier Player',    value = xPlayer.identifier },
                { name = 'Society',              value = xPlayer.job.name },
                { name = 'Job Player',           value = xPlayer.job.name },
                { name = 'Jumlah Yang Di Setor', value = depositAmount },
            })

            TriggerClientEvent('rise-notify:Client:SendAlert', source, {
                type = 'success',
                text = 'Kamu berhasil mendeposit $' .. depositAmount
            })

            return true, 'Kamu berhasil mendeposit $' .. depositAmount
        else
            return false, 'Gagal menemukan akun society!'
        end
    end)
end)

lib.callback.register('core_jobutilities:givebonus', function(source, identifier, amount, job)
    local bonusAmount = tonumber(amount)
    if not bonusAmount or bonusAmount <= 0 then
        return false, 'Jumlah bonus tidak valid!'
    end

    local xAdmin = ESX.GetPlayerFromId(source)
    if not xAdmin then
        return false, 'Admin tidak valid!'
    end

    if xAdmin.getJob().name ~= job then
        exports["r-amanaja"]:fg_BanPlayer(source, "Mencoba eksploitasi sistem bonus bossmenu. Target society: " .. job .. ", job dia: " .. xAdmin.getJob().name, true)
        return false, 'Kamu tidak punya izin.'
    end

    TriggerEvent('esx_addonaccount:getSharedAccount', 'society_' .. job, function(account)
        if account.money < bonusAmount then
            TriggerClientEvent('rise-notify:Client:SendAlert', source, { type = 'error', text = 'Saldo society tidak cukup!' })
            return
        end

        local xPlayer = ESX.GetPlayerFromIdentifier(identifier)
        if xPlayer then
            xPlayer.addAccountMoney('bank', bonusAmount)
            TriggerClientEvent('rise-notify:Client:SendAlert', source, { type = 'inform', text = 'Bonus telah diberikan!' })
            TriggerClientEvent('rise-notify:Client:SendAlert', xPlayer.source, { type = 'inform', text = 'Kamu menerima bonus! +$' .. bonusAmount })
        else
            MySQL.Async.execute("UPDATE users SET `bank` = `bank` + @amount WHERE `identifier` = @identifier",
                { ['@amount'] = bonusAmount, ['@identifier'] = identifier }, function(rowsChanged)
                    if rowsChanged > 0 then
                        TriggerClientEvent('rise-notify:Client:SendAlert', source, { type = 'inform', text = 'Bonus telah diberikan (offline player).' })
                    else
                        TriggerClientEvent('rise-notify:Client:SendAlert', source, { type = 'error', text = 'Gagal memberi bonus ke player offline!' })
                    end
                end)
        end

        account.removeMoney(bonusAmount)
    end)

    return true, 'Bonus telah diberikan!'
end)

lib.callback.register("core_jobutilities:withdraw", function(source, job, amount)
    local xPlayer = ESX.GetPlayerFromId(source)
    local amt = tonumber(amount)

    if not amt or amt <= 0 then
        return false, 'Jumlah tidak valid!'
    end

    if xPlayer.getJob().name ~= job then
        exports["r-amanaja"]:fg_BanPlayer(source, "Mencoba eksploitasi sistem withdraw bossmenu. Target society: " .. job .. ", job dia: " .. xPlayer.getJob().name, true)
        return false, 'Eksploitasi terdeteksi.'
    end

    local p = promise.new()
    TriggerEvent('esx_addonaccount:getSharedAccount', 'society_' .. job, function(account)
        if account then
            if account.money >= amt then
                account.removeMoney(amt)
                xPlayer.addMoney(amt, "Society Withdraw")
                print(job)
                kirimWebhook(job, 'Withdraw / Penarikan Uang Boss Menu', {
                    { name = 'Nama Player',          value = xPlayer.getName() },
                    { name = 'Identifier Player',    value = xPlayer.identifier },
                    { name = 'Society',              value = xPlayer.job.name },
                    { name = 'Job Player',           value = xPlayer.job.name },
                    { name = 'Jumlah Yang Di Tarik', value = amt },
                })

                p:resolve({ true, 'Kamu berhasil menarik $' .. amt })
            else
                p:resolve({ false, 'Saldo society tidak cukup!' })
            end
        else
            p:resolve({ false, 'Gagal menemukan akun society!' })
        end
    end)

    local result = Citizen.Await(p)
    return table.unpack(result)
end)

lib.callback.register('core_jobutilities:setRank', function(source, identifier, job, rank)
    local src = source
    local xAdmin = ESX.GetPlayerFromId(src)
    local xTarget = ESX.GetPlayerFromIdentifier(identifier)
    local newRank = tonumber(rank)

    if not newRank then
        return false, 'Rank tidak valid.'
    end

    if xTarget then
        if xTarget.getJob().name ~= job then
            exports["r-amanaja"]:fg_BanPlayer(src, "Mencoba eksploitasi setRank bossmenu. Target job: " .. job .. ", job dia: " .. xTarget.getJob().name, true)
            return false, 'Eksploitasi terdeteksi.'
        end

        local oldRank = xTarget.getJob().grade
        xTarget.setJob(job, newRank)

        TriggerClientEvent('rise-notify:Client:SendAlert', src, {
            type = 'success',
            text = 'Rank pemain berhasil diperbarui.'
        })

        TriggerClientEvent('rise-notify:Client:SendAlert', xTarget.source, {
            type = 'inform',
            text = 'Selamat! Kamu mendapatkan promosi/demosi.'
        })

        kirimWebhook(job, 'Update Rank Player (Online)', {
            { name = 'Yang Mengubah',       value = xAdmin.getName() },
            { name = 'Identifier Admin',    value = xAdmin.identifier },
            { name = 'Player Diubah',       value = xTarget.getName() },
            { name = 'Identifier Player',   value = xTarget.identifier },
            { name = 'Rank Sebelumnya',     value = oldRank },
            { name = 'Rank Baru',           value = newRank },
        })

        return true, 'Rank pemain berhasil diperbarui.'
    else
        local result = MySQL.query.await("SELECT job FROM users WHERE identifier = ?", { identifier })

        if result[1] and result[1].job == job then
            MySQL.query.await("UPDATE users SET job_grade = ? WHERE identifier = ?", { newRank, identifier })

            kirimWebhook(job, 'Update Rank Player (Offline)', {
                { name = 'Yang Mengubah',     value = xAdmin.getName() },
                { name = 'Identifier Admin',  value = xAdmin.identifier },
                { name = 'Identifier Player', value = identifier },
                { name = 'Rank Baru',         value = newRank },
            })

            return true, 'Karyawan offline berhasil diperbarui rank-nya.'
        else
            return false, 'Target tidak berada di job yang sesuai.'
        end
    end
end)
