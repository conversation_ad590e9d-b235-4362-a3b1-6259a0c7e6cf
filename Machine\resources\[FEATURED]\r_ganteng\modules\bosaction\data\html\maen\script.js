
var job = 'unemployed';
var grade = 3;




$(document).keyup(function(e) {
    if (e.keyCode === 27) {

        $.post('https://r_ganteng/close', JSON.stringify({}));
        $("#main_container").fadeOut("slow", function() {

            $("#main_container").html("").fadeIn();
        });

    }


});

function playClickSound() {
    var audio = document.getElementById("audio");
    audio.volume = 0.1;
    audio.play();
}

function openBossMenu(job, label, employees, fund, canwithdraw, candeposit, canhire, canrank, canfire, ranks, canbonus) {
    currentRanks = ranks;
    currentBossMenuJob = job;

    var base = `
        <div class="clearfix" id="page">
            <!-- group -->
            <div class="clearfix grpelem slide-top" id="pu657">
                <!-- column -->
                ${canhire ?
                    `<div id="addemployee" onclick="openHire()" class="ripple"><i class="fas fa-plus" style="margin-top: 12px; color: white;"></i></div>` :
                    `<div id="addemployee" style="opacity: 0.5;" class="ripple"><i class="fas fa-plus" style="margin-top: 12px;"></i></div>`
                }

                <div class="gradient rounded-corners grpelem" id="u654"></div>
                <div class="colelem" id="u657"></div>
                <div class="clearfix colelem" id="u660-4">
                    <!-- content -->
                    <p>${label.toUpperCase()}</p>
                </div>
                <div class="clearfix colelem" id="u667-4">
                    <!-- content -->
                    <p>FUNDS</p>
                </div>
                <div class="clearfix colelem" id="u663-4">
                    <!-- content -->
                    $${fund}
                </div>
                <div class="clearfix colelem" id="pu670-4">
                    <!-- group -->
                    ${canwithdraw ?
                        `<button class="rounded-corners grpelem ripple" onclick="openWithdraw()" id="u670-4">
                            <p id="u670-2">WITHDRAW</p>
                        </button>` :
                        `<button class="rounded-corners grpelem ripple" style="opacity: 0.5;" id="u670-4">
                            <p id="u670-2">WITHDRAW</p>
                        </button>`
                    }

                    ${candeposit ?
                        `<button class="rounded-corners grpelem ripple" onclick="openDeposit()" id="u673-4">
                            <p id="u673-2">DEPOSIT</p>
                        </button>` :
                        `<button class="rounded-corners grpelem ripple" style="opacity: 0.5;" id="u673-4">
                            <p id="u673-2">DEPOSIT</p>
                        </button>`
                    }
                </div>
                <div class="clearfix colelem" id="u688-4">
                    <!-- content -->
                    <p>EMPLOYEES</p>
                </div>
                <div class="clearfix colelem" id="employee-search" style="position: relative; z-index: 1001; margin-bottom: 10px; margin-top: 8px; width: 100%;">
                    <input type="text" id="searchEmployeeInput" placeholder="Search employee..." style="width: 100%; padding: 8px; border-radius: 8px; border: 1px solid #ccc; box-shadow: 0 2px 4px rgba(0,0,0,0.1); z-index: 1001; position: relative;">
                </div>
                <div class="clearfix colelem" id="u682">
                    <!-- column -->
                    ${employees.map((employee) => {
                        return `
                            <div class="rounded-corners clearfix colelem employee-item" data-fullname="${employee.fullname.toLowerCase()}" data-grade="${employee.grade_label.toLowerCase()}" id="u745">
                                <!-- group -->
                                <div class="clearfix grpelem" id="pu746-4">
                                    <!-- column -->
                                    <div class="clearfix colelem" id="u746-4">
                                        <!-- content -->
                                        <p><span id="u746">${employee.fullname}</span></p>
                                    </div>
                                    <div class="clearfix colelem" id="u747-4">
                                        <!-- content -->
                                        <p>${employee.grade_label}</p>
                                    </div>
                                </div>
                                ${canrank ?
                                    `<div class="grpelem ripple" data-identifier="${employee.identifier}" onclick="openRanks(this)" id="u749">
                                        <i class="fas fa-layer-group" style="color: white;"></i>
                                    </div>` : ''
                                }

                                ${canfire ?
                                    `<div class="grpelem ripple" data-identifier="${employee.identifier}" onclick="sureWindow(this)" id="u748">
                                        <i class="fas fa-times fa-lg" style="color: white;"></i>
                                    </div>` : ''
                                }

                                ${canbonus ?
                                    `<div class="grpelem ripple" data-identifier="${employee.identifier}" onclick="openBonus(this)" id="u747">
                                        <i class="fas fa-hand-holding-usd"></i>
                                    </div>` : ''
                                }
                            </div>
                        `;
                    }).join('')}
                </div>
            </div>
            <div class="verticalspacer" data-offset-top="0" data-content-above-spacer="712" data-content-below-spacer="18"></div>
        </div>
    `;

    $("#main_container").html(base);

    $('#searchEmployeeInput').on('input', function () {
        const keyword = $(this).val().toLowerCase();
        $('.employee-item').each(function () {
            const name = $(this).attr('data-fullname') || '';
            const grade = $(this).attr('data-grade') || '';

            if (name.includes(keyword) || grade.includes(keyword)) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });
}

function openHire() {

    playClickSound();


        var base = '<div id="input">' +


        '<div id="darken"><div>' +
        '<div id="inputfield" class="slide-top">' +

'        <p id="inputtext">HIRE</p>'+
'<i class="fas fa-id-badge fa-lg" id="inputdollar" style="color: white;"></i>' +
'<input type="text" id="inputin" placeholder="ID"></input>' +
'     <button class="rounded-corners  grpelem ripple" onclick="hireperson(this)" id="u673-5"><!-- content -->'+
'      <p id="u673-2">HIRE</p>'+
'     </button>'+

        '</div>' +
        '</div>';

        $("#pu657").append(base);


}

function openBonus(t) {

playClickSound();

        var base = '<div id="input">' +


        '<div id="darken"><div>' +
        '<div id="inputfield" class="slide-top">' +

'        <p id="inputtext">ENTER BONUS</p>'+
'<i class="fas fa-dollar-sign fa-lg" id="inputdollar"></i>' +
'<input type="text" id="inputin" placeholder="0"></input>' +
'     <button class="rounded-corners  grpelem ripple" data-identifier="'+t.dataset.identifier+'" onclick="givebonus(this)" id="u673-5"><!-- content -->'+
'      <p id="u673-2">GIVE</p>'+
'     </button>'+

        '</div>' +
        '</div>';

        $("#pu657").append(base);


}

function openWithdraw() {

playClickSound();

        var base = '<div id="input">' +


        '<div id="darken"><div>' +
        '<div id="inputfield" class="slide-top">' +

'        <p id="inputtext">WITHDRAW</p>'+
'<i class="fas fa-dollar-sign fa-lg" id="inputdollar"></i>' +
'<input type="text" id="inputin" placeholder="0"></input>' +
'     <button class="rounded-corners  grpelem ripple" onclick="withdrawAmount(this)" id="u673-5"><!-- content -->'+
'      <p id="u673-2">WITHDRAW</p>'+
'     </button>'+

        '</div>' +
        '</div>';

        $("#pu657").append(base);


}

function openDeposit() {

    playClickSound();


        var base = '<div id="input">' +


        '<div id="darken"><div>' +
        '<div id="inputfield" class="slide-top">' +

'        <p id="inputtext">DEPOSIT</p>'+
'<i class="fas fa-dollar-sign fa-lg" id="inputdollar"></i>' +
'<input type="text" id="inputin" placeholder="0"></input>' +
'     <button class="rounded-corners  grpelem ripple" onclick="depositAmount(this)" id="u673-5"><!-- content -->'+
'      <p id="u673-2">DEPOSIT</p>'+
'     </button>'+

        '</div>' +
        '</div>';

        $("#pu657").append(base);


}

function sureWindow(t) {

    playClickSound();


        var base = '<div id="input">' +


        '<div id="darken"><div>' +
        '<div id="inputfield" class="slide-top">' +

'        <p id="inputtext">ARE YOU SURE?</p>'+
'        <p id="inputothertext">You want to fire ' +$(t).parent().find("#u746").text()+'</p>'+

'     <button class="rounded-corners" data-identifier="'+t.dataset.identifier+'"  grpelem ripple" onclick="fire(this)" id="u673-5"><!-- content -->'+
'      <p id="u673-2">YES</p>'+
'     </button>'+

        '</div>' +
        '</div>';

        $("#pu657").append(base);


}

function openRanks(t) {

    playClickSound();


        var base = '<div id="input">' +


        '<div id="darken"><div>' +
        '<div id="inputfield" class="slide-top">' +

'        <p id="inputtext">RANKS</p>'+
'      <div id="ranklist">';

for (i = 0; i < currentRanks.length; i++) {

base = base + '<div id="rankentry" data-identifier="'+t.dataset.identifier+'" data-grade="'+currentRanks[i].grade+'" onclick="promote(this)" class="ripple">'+currentRanks[i].grade_label.toUpperCase()+'</div>';
}

base = base +'</div>' +


        '</div>' +
        '</div>';

        $("#pu657").append(base);


}

function hireperson(t) {

    playClickSound();

        var id = $(t).parent().find('#inputin').val();

        // Close the input dialog but keep the main boss menu open
        $("#input").remove();

        $.post('https://r_ganteng/hire', JSON.stringify({job: currentBossMenuJob, id: id}));
}

function withdrawAmount(t) {

    playClickSound();

        var amount = $(t).parent().find('#inputin').val();

        // Close the input dialog but keep the main boss menu open
        $("#input").remove();

        $.post('https://r_ganteng/withdraw', JSON.stringify({job: currentBossMenuJob, amount: amount}));
}


function givebonus(t) {

    playClickSound();

        var amount = $(t).parent().find('#inputin').val();
        var identifier = t.dataset.identifier;

        // Close the input dialog but keep the main boss menu open
        $("#input").remove();

        $.post('https://r_ganteng/givebonus', JSON.stringify({identifier: identifier, amount: amount, job: currentBossMenuJob}));
}


function depositAmount(t) {

    playClickSound();

        var amount = $(t).parent().find('#inputin').val();

        // Close the input dialog but keep the main boss menu open
        $("#input").remove();

        $.post('https://r_ganteng/deposit', JSON.stringify({job: currentBossMenuJob, amount: amount}));
}

function fire(t) {

    playClickSound();

        var identifier = t.dataset.identifier;

        // Close the confirmation dialog but keep the main boss menu open
        $("#input").remove();

        $.post('https://r_ganteng/fire', JSON.stringify({identifier: identifier, job: currentBossMenuJob}));


}

function promote(t) {

    playClickSound();

        var rank = t.dataset.grade;
        var identifier = t.dataset.identifier;

        // Close the ranks dialog but keep the main boss menu open
        $("#input").remove();

        $.post('https://r_ganteng/setrank', JSON.stringify({identifier: identifier, rank: rank, job: currentBossMenuJob}));


}

function openJobCenter(defaultJobs) {


    var base = '<div id="jobcenter">';



    for (i = 0; i < defaultJobs.length; i++) {

        var text = 'SELECT';


            if (defaultJobs[i].job == job) {
                text = 'SELECTED';
            }


        base = base + '<div class="shadow gradient rounded-corners colelem animated fadeInUp" id="u712">' +
            '    <div class="clearfix grpelem" id="u465-4"><!-- content -->' +
            '      <p>' + defaultJobs[i].label + '</p>' +
            '     </div>' +
            '     <button class="rounded-corners grpelem ripple addjob" data-job="' + defaultJobs[i].job + '" id="u468-4"><!-- content -->' +
            text +
            '     </button>' +
            '     <div class="grpelem" id="u474"><i class="' + defaultJobs[i].icon + ' fa-3x"></i><!-- simple frame --></div>' +
            '     <div class="clearfix grpelem" id="u477-4"><!-- content -->' +
            '      <p>' + defaultJobs[i].description + '</p>' +
            '     </div>' +
            '     </div>';

    }




    base = base + '   </div>';


    $("#main_container").html(base);
}



window.addEventListener('message', function(event) {

    var edata = event.data;


    if (edata.type == "openBoss") {

        job = edata.job.job;
        grade = edata.job.grade;




       openBossMenu(edata.bossJob, edata.bossLabel, edata.employees, edata.fund, edata.perms.canWithdraw, edata.perms.canDeposit, edata.perms.canHire, edata.perms.canRank, edata.perms.canFire, edata.grades, edata.perms.canBonus);


    }

    // Handle refresh boss menu with updated data
    if (edata.type == "refreshBoss") {

        console.log("[BOSSMENU JS] Received refreshBoss event with " + edata.employees.length + " employees");

        job = edata.job.job;
        grade = edata.job.grade;

       openBossMenu(edata.bossJob, edata.bossLabel, edata.employees, edata.fund, edata.perms.canWithdraw, edata.perms.canDeposit, edata.perms.canHire, edata.perms.canRank, edata.perms.canFire, edata.grades, edata.perms.canBonus);

    }

    if (edata.type == "openCenter") {

        job = edata.job.job;
        grade = edata.job.grade;
        offduty = edata.offduty;

        const centerJobs = JSON.parse(edata.center);



        openJobCenter(centerJobs);




    }


    $(".addjob").click(function() {
        playClickSound();

        if ($(this).text().replace(/ /g, '') != 'SELECTED') {
            $(document).find(".addjob").text('SELECT');
            $(this).text('SELECTED');
            $.post('https://r_ganteng/addjob', JSON.stringify({
                job: this.dataset.job

            }));
        }

    });




});